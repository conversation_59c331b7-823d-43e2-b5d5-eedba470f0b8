import { inngest } from "../../client";
import { Organization, User } from "@/models";
import IntegrationMapping, { INTEGRATION_OBJECT_TYPE } from "@/models/integrationMapping";
import { QuickBooksOnlineService } from "@/services/integrations/quickBooksOnline";
import { IntegrationSettingsService } from "@/services/integrations/settings";
import { RutterService } from "@/services/integrations/rutter";
import * as Sentry from "@sentry/node";
import { checkCache, CheckService } from "@/services/check";
import { CheckEmployee } from "@/types/check";

export const syncEmployees = inngest.createFunction(
  { id: "sync-employees-to-integrations" },
  { event: "integration/employees-sync" },
  async ({ event }) => {
    const { organizationId } = event.data;

    // As soon as user enables the Employees setting sync on the main settings tab, we should fetch all employees from QBO,
    // Match them against users in Hammr based on an exact, case insensitive match of first name and last name.
    // For users that have a match, create a mapping between the Hammr user and the QBO user in our mapping table.
    try {
      const quickBooksOnlineService = new QuickBooksOnlineService();
      const integrationSettingsService = new IntegrationSettingsService();
      const rutterService = new RutterService();

      const integrationEmployeesSync = await integrationSettingsService.getIntegrationObjectSetting(
        organizationId,
        "EMPLOYEES"
      );
      const integration = integrationEmployeesSync.integration;

      if (!integration.isEnabled || !integrationEmployeesSync?.isEnabled) {
        return {
          status: "skipped",
          reason: "Employees sync not enabled",
        };
      }

      // Get all Hammr employees for this organization
      const hammrEmployees = await User.findAll({
        where: {
          organizationId,
          isArchived: false,
        },
      });

      const qboEmployees = (await quickBooksOnlineService.employees(organizationId)) ?? [];

      // Get existing employee mappings
      const existingMappings = await IntegrationMapping.findAll({
        where: {
          integrationUserTokenId: integration.id,
          objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
        },
      });

      const existingMappingsByHammrId = Object.fromEntries(
        existingMappings.map((mapping) => [mapping.hammrId, mapping])
      ) as Record<number, IntegrationMapping>;

      // preload employees from Check. This will cache all the employees in the CheckService
      const organization = await Organization.findByPk(organizationId);
      const checkEmployees = await new CheckService().loadAllPaginatedData<CheckEmployee>(`/employees`, {
        company: organization.checkCompanyId
      });
      checkEmployees.forEach((employee) => {
        checkCache.set(`/employees/${employee.id}`, {
          value: employee,
          expiry: Date.now() + 60 * 1000, // cache for 1 min
        });
      });

      // Process each Hammr employee
      const employeePromises = hammrEmployees.map(async (hammrEmployee) => {
        // If the employee already has a mapping, skip
        if (existingMappingsByHammrId[hammrEmployee.id]) {
          return {
            status: "success",
            reason: "Already mapped",
          };
        }

        // Find matching QBO employee by first name and last name (case insensitive)
        const matchingQboEmployee = qboEmployees.find(
          (qboEmployee) =>
            qboEmployee.GivenName.toLowerCase() === hammrEmployee.firstName.toLowerCase() &&
            (!hammrEmployee.lastName ||
              !qboEmployee.FamilyName ||
              qboEmployee.FamilyName.toLowerCase() === hammrEmployee.lastName.toLowerCase())
        );

        // if a Hammr employee doesn't exist in QBO, create it
        if (!matchingQboEmployee) {
          await rutterService.createEmployee(hammrEmployee);

          return {
            status: "success",
            action: "Created employee in integration",
          };
        } else {
          // Create a mapping between the Hammr user and QBO employee
          await IntegrationMapping.create({
            hammrId: hammrEmployee.id,
            objectType: INTEGRATION_OBJECT_TYPE.EMPLOYEE,
            externalId: matchingQboEmployee.Id,
            integrationUserTokenId: integration.id,
          });

          return {
            status: "success",
            action: "Created mapping to existing integration employee",
            externalId: matchingQboEmployee.Id,
          };
        }
      });

      const results = await Promise.allSettled(employeePromises);
      const uniqueErrorsMap = new Map();
      results.forEach((result, index) => {
        if (result.status === "rejected") {
          const error = result.reason;
          const errorKey = error.message || String(error);

          uniqueErrorsMap.set(errorKey, {
            error,
            employeeId: hammrEmployees[index].id,
          });
        }
      });

      // Log each unique error to Sentry with context
      uniqueErrorsMap.forEach(({ error, employeeId }) => {
        Sentry.captureException(error, {
          tags: {
            function: "syncEmployees",
            organizationId,
            employeeId,
          },
          extra: {
            operation: "employee-sync",
          },
        });
      });

      // process the errors and add more info to them
      const processedResults = results.map((result, index) => {
        const employeeId = hammrEmployees[index].id;
        if (result.status === "fulfilled") {
          return {
            employeeId,
            ...result.value,
          };
        } else {
          return {
            employeeId,
            status: "error",
            error: result.reason?.message || String(result.reason),
          };
        }
      });

      return {
        status: "completed",
        results: processedResults,
        errorCount: uniqueErrorsMap.size,
      };
    } catch (error) {
      Sentry.captureException(error);

      return {
        status: "failed",
        error: error.message,
      };
    }
  }
);
