import {
  <PERSON>B<PERSON>sCust<PERSON>,
  QuickBooksEmployee,
  QuickBooksEmployeeForm,
  QuickBooksJournalEntry,
  QuickBooksJournalEntryResponse,
  QuickBooksTimeActivity,
  QuickBooksTimeActivityData,
} from "./quickBooks.types";
import { HTTPService } from "./httpService";

const { QUICKBOOKS_API_URL: QUICKBOOKS_API_URL } = process.env;

export class QuickBooksOnline extends HTTPService {
  protected baseUrl = `${QUICKBOOKS_API_URL}`;

  protected getHeaders() {
    return {
      "Content-Type": "application/json",
      Accept: "application/json",
    };
  }

  async createEmployee(
    authToken: string,
    realm: string,
    employee: {
      firstName: string;
      lastName?: string;
      address?: { line1?: string; city?: string; state?: string; postalCode?: string; country?: string };
    }
  ) {
    const requestBody: QuickBooksEmployeeForm = {
      GivenName: employee.firstName,
      FamilyName: employee.lastName ?? "",
    };

    // Add address information if provided
    if (employee.address) {
      const { line1, city, state, postalCode, country } = employee.address;
      requestBody.PrimaryAddr = {
        Line1: line1,
        City: city,
        CountrySubDivisionCode: state,
        PostalCode: postalCode,
        Country: country,
      };
    }

    return await this.post<{ Employee: QuickBooksEmployee }>(`v3/company/${realm}/employssee`, requestBody, undefined, {
      Authorization: `Bearer ${authToken}`,
    }).then((response) => response.Employee);
  }

  async getEmployee(authToken: string, realm: string, id: string) {
    // We can't use this now because the SyncToken returned by this endpoint is not correct
    // return await this.get<{
    //   Employee: QuickBooksEmployee;
    // }>(`v3/company/${realm}/employee/${id}`, undefined, { Authorization: `Bearer ${authToken}` }).then(
    //   (response) => response.Employee
    // );

    const query = `SELECT *
                   FROM Employee
                   WHERE Id = '${id}'`;

    return await this.get<{
      QueryResponse: { Employee?: QuickBooksEmployee[] };
    }>(`v3/company/${realm}/query?query=${query}`, undefined, { Authorization: `Bearer ${authToken}` }).then(
      (response) => response.QueryResponse.Employee?.[0]
    );
  }

  async getEmployeeByDisplayName(authToken: string, realm: string, displayName: string) {
    const query = `SELECT *
                   FROM Employee
                   WHERE DisplayName = '${displayName}'`;

    return await this.get<{
      QueryResponse: { Employee: QuickBooksEmployee[] };
    }>(`v3/company/${realm}/query?query=${query}`, undefined, { Authorization: `Bearer ${authToken}` }).then(
      (response) => response.QueryResponse.Employee[0]
    );
  }

  async projects(authToken: string, realm: string) {
    return this.fetchAllPages<QuickBooksCustomer>(
      authToken,
      realm,
      `SELECT *
       FROM Customer
       WHERE Job = true
         AND Active = true`,
      "Customer"
    );
  }

  async employees(authToken: string, realm: string) {
    return this.fetchAllPages<QuickBooksEmployee>(
      authToken,
      realm,
      `SELECT *
       FROM Employee
       WHERE Active = true`,
      "Employee"
    );
  }

  private async fetchAllPages<T>(
    authToken: string,
    realm: string,
    baseQuery: string,
    entityName: string,
    startPosition = 1,
    allResults: T[] = []
  ): Promise<T[]> {
    const MAX_RESULTS = 1000;
    const query = `${baseQuery} STARTPOSITION ${startPosition} MAXRESULTS ${MAX_RESULTS}`;

    const response = await this.get<{
      QueryResponse: { [key: string]: T[] } & { maxResults: number };
    }>(`v3/company/${realm}/query?query=${encodeURIComponent(query)}`, undefined, {
      Authorization: `Bearer ${authToken}`,
    });

    const entities = response.QueryResponse[entityName] || [];
    const updatedResults = [...allResults, ...entities];

    // Check if we need to fetch more pages
    if (response.QueryResponse.maxResults === MAX_RESULTS) {
      // Recursively fetch the next page
      return this.fetchAllPages<T>(
        authToken,
        realm,
        baseQuery,
        entityName,
        startPosition + MAX_RESULTS,
        updatedResults
      );
    }

    return updatedResults;
  }

  async updateEmployee(
    authToken: string,
    realm: string,
    id: string,
    employee: {
      firstName: string;
      lastName?: string;
      isArchived: boolean;
    }
  ) {
    const qboEmployee = await this.getEmployee(authToken, realm, id);

    return await this.post<{ Employee: QuickBooksEmployee }>(
      `v3/company/${realm}/employee`,
      {
        Id: id,
        // wen need this due to this error: https://help.developer.intuit.com/s/question/0D5G000004Dk75mKAB/why-am-i-getting-this-response-you-and-craig-carlson-were-working-on-this-at-the-same-time
        SyncToken: qboEmployee.SyncToken,
        GivenName: employee.firstName,
        FamilyName: employee.lastName ?? "",
        Active: !employee.isArchived,
        DisplayName: `${employee.firstName} ${employee.lastName ?? ""}`,
      },
      undefined,
      { Authorization: `Bearer ${authToken}` }
    ).then((response) => response.Employee);
  }

  async getTimeActivity(authToken: string, realm: string, id: string) {
    return await this.get<{
      TimeActivity: QuickBooksTimeActivity;
    }>(`v3/company/${realm}/timeactivity/${id}`, undefined, { Authorization: `Bearer ${authToken}` }).then(
      (response) => response.TimeActivity
    );
  }

  async createTimeActivity(authToken: string, realm: string, timeActivity: QuickBooksTimeActivityData) {
    const data: Record<string, any> = {
      NameOf: "Employee",
      StartTime: timeActivity.startTime,
      EndTime: timeActivity.endTime,
      TxnDate: timeActivity.txnDate,
      BreakHours: timeActivity.breakHours,
      BreakMinutes: timeActivity.breakMinutes,
      EmployeeRef: timeActivity.employeeRef,
    };

    if (timeActivity.customerRef) {
      data.CustomerRef = timeActivity.customerRef;
    }

    return await this.post<{ TimeActivity: QuickBooksTimeActivity }>(
      `v3/company/${realm}/timeactivity`,
      data,
      undefined,
      { Authorization: `Bearer ${authToken}` }
    ).then((response) => response.TimeActivity);
  }

  async updateTimeActivity(authToken: string, realm: string, id: string, timeActivity: QuickBooksTimeActivityData) {
    const qboTimeActivity = await this.getTimeActivity(authToken, realm, id);

    const data: Record<string, any> = {
      Id: id,
      SyncToken: qboTimeActivity.SyncToken,
      NameOf: "Employee",
      StartTime: timeActivity.startTime,
      EndTime: timeActivity.endTime,
      TxnDate: timeActivity.txnDate,
      BreakHours: timeActivity.breakHours,
      BreakMinutes: timeActivity.breakMinutes,
      EmployeeRef: timeActivity.employeeRef,
    };

    if (timeActivity.customerRef) {
      data.CustomerRef = timeActivity.customerRef;
    }
    if (timeActivity.projectRef) {
      data.ProjectRef = timeActivity.projectRef;
    }

    return await this.post<{ TimeActivity: QuickBooksTimeActivity }>(
      `v3/company/${realm}/timeactivity`,
      data,
      undefined,
      { Authorization: `Bearer ${authToken}` }
    ).then((response) => response.TimeActivity);
  }

  /**
   * Creates a journal entry in QuickBooks Online
   *
   * @param authToken OAuth2 access token for QuickBooks API
   * @param realm Company ID (realm) for QuickBooks
   * @param journalEntry Journal entry data to create
   * @returns Created journal entry from QuickBooks
   */
  async createJournalEntry(authToken: string, realm: string, journalEntry: QuickBooksJournalEntry) {
    return await this.post<QuickBooksJournalEntryResponse>(
      `v3/company/${realm}/journalentry?minorversion=75`,
      journalEntry,
      undefined,
      { Authorization: `Bearer ${authToken}` }
    ).then((response) => response.JournalEntry);
  }
}

export const errorMappings: Record<string, string> = {
  "Duplicate Name Exists Error": "An employee with this name already exists",
};
