import fetch, { RequestInit, Response } from "node-fetch";

import { Organization } from "@/models";

import {
  hammrCategoryToCheckBenefitType,
  mappingsSelector,
  propertyIsPartOfAddress,
} from "@/util/hammrToCheckMappings";

import { DownstreamService, DownstreamServiceConfig } from "@/services/orchestrator";

import {
  CreateContractorDto,
  CreateEmployeeDto,
  UpdateCompanyDto,
  UpdateContractorDto,
  UpdateEmployeeDto,
} from "@/types/checkDto";
import { CheckContractor, CheckEmployee } from "@/types/check";

interface SpecialMappingFunction {
  // eslint-disable-next-line no-unused-vars
  (key: string, value: any, payload?: any): any;
}

export interface UpdateResourceObj {
  resourceId?: string;
  type?: string;
  resourceName: string;
  method: string;
  endpoint: string;
  data: any;
}

export interface PayloadData {
  resourceId?: string;
  type?: string; // can be used for any identifier - ex: reg vs ot earning rate
  data: any;
  method: string;
}

export interface PayrollUpdateResult {
  status: "success";
  resourceName: string;
  resourceId: string;
  method: string;
  data?: any;
  error?: any;
}

export interface PaginatedResponse<T> {
  next: string | null;
  previous: string | null;
  results: T[];
}

const ALLOWED_CHECK_ORIGINS = ["https://render-sandbox.checkhq.com", "https://render.checkhq.com"];

// this is used to cache GET requests
export const checkCache = new Map<string, { value: any; expiry: number }>();

export class CheckService implements DownstreamService {
  private baseUrl: string;
  private apiKey: string;
  private mappingToUse: Record<string, Record<string, string>>;

  constructor() {
    this.mappingToUse = mappingsSelector;
    this.baseUrl = process.env.CHECK_API_URL || "";
    this.apiKey = process.env.CHECK_API_KEY || "";
  }

  private isAllowedCheckUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);

      return ALLOWED_CHECK_ORIGINS.includes(urlObj.origin as typeof ALLOWED_CHECK_ORIGINS[number]);
    } catch {
      return false;
    }
  }

  private async requestWithFullUrl(method: string, fullUrl: string, options: RequestInit = {}) {
    if (!this.isAllowedCheckUrl(fullUrl)) {
      throw new Error("URL origin not in allowlist");
    }

    const response = await fetch(fullUrl, {
      ...options,
      method,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        ...options.headers,
      },
    });

    return response;
  }

  getForwardingConfiguration({ endpoint, method }: DownstreamServiceConfig) {
    return {
      endpoint,
      method,
      transformData: (data: any) => {
        // Transform the data to the format expected by CheckService
        return { transformed: data };
      },
    };
  }

  private async request(method: string, endpoint: string, data?: any) {
    // const endppoint = `${this.baseUrl}${endpoint}`;
    // console.log(`Making request ${method} to ${endppoint} with ${JSON.stringify(data)}`); // keep because helpful for debugging
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    let json;

    try {
      json = await response.json();
    } catch (parseError) {
      throw new Error("Failed to parse JSON response");
    }

    // preserve the error message from the Check API and rethrow
    if (json.error) {
      throw json.error;
    }

    return json;
  }

  public async get<T = any>(endpoint: string) {
    const cachedData = checkCache.get(endpoint);
    if (cachedData && cachedData.expiry > Date.now()) {
      return cachedData.value;
    }

    // cache the data first and then return
    const response = (await this.request("GET", endpoint)) as Promise<T>;
    checkCache.set(endpoint, {
      value: response,
      expiry: Date.now() + 60 * 1000, // cache for 1 min
    });

    return response;
  }

  public post<T = any>(endpoint: string, data: any): Promise<T> {
    // TODO remove `any` when we enable TS strict mode
    return this.request("POST", endpoint, data);
  }

  public patch(endpoint: string, data: any) {
    return this.request("PATCH", endpoint, data);
  }

  public delete(endpoint: string) {
    return this.request("DELETE", endpoint);
  }

  // Helper method to load all paginated data for a given endpoint recursively
  // This is needed because Check's API doesn't allow loading ALL the data for a given resource in one request
  public async loadAllPaginatedData<T>(endpoint: string, queryParams?: Record<string, any>): Promise<T[]> {
    let allResults: T[] = [];

    const searchParams = new URLSearchParams();
    searchParams.append("limit", "100");
    if (queryParams) {
      Object.entries(queryParams).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          value.forEach((item) => searchParams.append(key, item.toString()));
        } else if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
    }

    let url = endpoint;

    while (url) {
      const response = await this.get(`${url}?${searchParams}`);
      const urlInstance = response.next ? new URL(response.next) : undefined;
      url = urlInstance ? `${urlInstance.pathname}${urlInstance.search}` : undefined;
      allResults = allResults.concat(response.results ?? []);
    }

    return allResults;
  }

  // NEEDS SIGNIFICANT TESTING AFTER REFACTOR
  handleCreateCheckPayload(bodyData: any, targetedObject: string, method: string) {
    switch (targetedObject) {
      case "employees":
        return this.handleEmployeePayload(bodyData, method);
      case "contractors":
        return this.handleContractorPayload(bodyData, method);
      case "companies":
        return this.handleCompanyPayload(bodyData, method);
      case "earning_rates":
        return this.handleEarningRatePayload(bodyData, method);
      case "company_benefits":
        return this.handleCompanyBenefitPayload(bodyData, method);
      case "benefits":
        return this.handleEmployeeBenefitPayload(bodyData, method);
      case "earning_codes":
        return this.handleEarningCodePayload(bodyData, method);
      case "payrolls":
        return this.handlePayrollPayload(bodyData, method);
      default:
        throw new Error(`Unsupported entity type: ${targetedObject}`);
    }
  }

  private handleEmployeePayload(bodyData: any, method: string) {
    const payload = method === "POST" ? <CreateEmployeeDto>{} : <UpdateEmployeeDto>{};

    if (Object.prototype.hasOwnProperty.call(bodyData, "isArchived")) {
      bodyData.active = !bodyData.isArchived;
      delete bodyData.isArchived;
    }

    return this.mapPayload(bodyData, payload, "residence", "employees");
  }

  private handleContractorPayload(bodyData: any, method: string) {
    const payload = method === "POST" ? <CreateContractorDto>{} : <UpdateContractorDto>{};

    return this.mapPayload(bodyData, payload, "address", "contractors");
  }

  private handleCompanyPayload(bodyData: any, method: string) {
    if (method !== "PATCH") throw new Error("Unsupported method for companies");
    const payload = <UpdateCompanyDto>{};

    return this.mapPayload(bodyData, payload, "address", "companies");
  }

  private handleEarningRatePayload(bodyData: any, method: string) {
    if (method !== "POST" && method !== "PATCH") throw new Error("Unsupported method for earning rates");
    const payload = <any>{};

    // if bodyData.period is hourly, then we remove `weeklyHours` property (so this doesn't get passed to Check as null - Check throws)
    // Check defaults this to 40 hours, but really only applicable for salary
    // https://docs.checkhq.com/reference/create-an-earning-rate
    if (bodyData.period === "hourly") {
      delete bodyData.weeklyHours;
    }

    return this.mapPayload(bodyData, payload, "address", "earning_rates");
  }

  private handleCompanyBenefitPayload(bodyData: any, method: string) {
    if (method !== "POST" && method !== "PATCH") throw new Error("Unsupported method for company benefits");
    const payload = <any>{};

    const result = this.mapPayload(bodyData, payload, null, "company_benefits", this.specialCompanyBenefitMappings);

    // Only set period to monthly if contributionType is AMOUNT
    if (bodyData.contributionType === "AMOUNT") {
      result.period = "monthly";
    }

    return result;
  }

  private handleEmployeeBenefitPayload(bodyData: any, method: string) {
    if (method !== "POST" && method !== "PATCH") throw new Error("Unsupported method for employee benefits");
    const payload = <any>{};

    const result = this.mapPayload(bodyData, payload, null, "benefits", this.specialCompanyBenefitMappings);

    // we store period capitalized to be consistent with our ENUM style
    if (bodyData.period === "MONTHLY") {
      result.period = "monthly";
    }

    return result;
  }

  private handleEarningCodePayload(bodyData: any, method: string) {
    if (method !== "POST" && method !== "PATCH") throw new Error("Unsupported method for earning codes");
    const payload = <any>{};

    return this.mapPayload(bodyData, payload, null, "earning_codes");
  }

  private handlePayrollPayload(bodyData: any, method: string) {
    if (method !== "POST" && method !== "PATCH") throw new Error("Unsupported method for payrolls");
    const payload = <any>{};

    return this.mapPayload(bodyData, payload, null, "payrolls");
  }

  private mapPayload(
    bodyData: any,
    payload: any,
    addressKey: string,
    targetedObject: string,
    specialMappings?: SpecialMappingFunction
  ) {
    const currentMapping = this.mappingToUse[targetedObject];

    for (const [key, value] of Object.entries(bodyData)) {
      const mappedKey: string | undefined = currentMapping[key]; // Now correctly typed as string or undefined

      if (mappedKey) {
        if (propertyIsPartOfAddress(key)) {
          payload[addressKey] = payload[addressKey] || {};
          payload[addressKey][mappedKey] = value;
        } else {
          payload[mappedKey] = specialMappings ? specialMappings(key, value, bodyData) : value;
        }
      }
    }

    return payload;
  }

  private specialCompanyBenefitMappings(key: string, value: any) {
    if (key === "benefitStartDate" || key === "benefitEndDate") {
      // should already be YYYY-MM-DD or transfomrations would make this not work
      return value;
    }

    if (key === "employeeContributionPercent") {
      // check expects 2 decimal string
      if (typeof value === "string") {
        value = parseFloat(value);
        if (!isNaN(value)) {
          return value.toFixed(2);
        }
      }

      return value.toFixed(2);
    }

    if (key === "category") {
      return hammrCategoryToCheckBenefitType(value);
    }

    return value;
  }

  // always return updated object
  // the idea behind handleConcurrentPayrollUpdates should be a collection of data objects necessary to instruct how to execute the payroll update (concurrently)
  async handleConcurrentPayrollUpdates(updateCollection: Partial<UpdateResourceObj>[]) {
    // Use map to create an array of promises from the updateCollection
    const updatePromises = updateCollection.map((update) => {
      // Use the private request method to perform the operation
      return this.request(update.method, update.endpoint, update.data)
        .then(
          (response) =>
            ({
              status: "success",
              resourceName: update.resourceName,
              resourceId: update.resourceId,
              method: update.method,
              type: update.type || null,
              data: response,
            } as PayrollUpdateResult)
        )
        .catch((error) => ({
          status: "error",
          resourceName: update.resourceName,
          resourceId: update.resourceId,
          method: update.method,
          type: update.type || null,
          error: error.message || "Unknown error",
        }));
    });

    // Execute all the update operations concurrently
    return Promise.all(updatePromises);
  }

  // the idea is earning rates are "immutable" meaning you can't change the amount, but you can change the active status and the "name" or the earning rate
  generateEarningRateUpdateData(payloadCollection: PayloadData[]) {
    // This is a placeholder for the actual implementation
    const mappedEarningRateUpdateResources = payloadCollection.map((payload) => {
      const updateObj: Partial<UpdateResourceObj> = {
        resourceName: "earning_rates",
        method: payload.method,
        data: this.handleCreateCheckPayload(payload.data, "earning_rates", payload.method),
      };

      if (payload.method === "PATCH") {
        updateObj.endpoint = `/earning_rates/${payload.resourceId}`;
        updateObj.resourceId = payload.resourceId;
      }

      if (payload.method === "POST") {
        updateObj.endpoint = "/earning_rates";
      }

      if (payload.type) {
        updateObj.type = payload.type;
      }

      return updateObj;
    });

    return mappedEarningRateUpdateResources;
  }

  generateEmployeeUpdateData(payloadCollection: PayloadData[]) {
    const mappedEmployeeUpdateResources = payloadCollection.map((payload) => {
      const updateObj: Partial<UpdateResourceObj> = {
        resourceName: "employees",
        method: payload.method,
        data: this.handleCreateCheckPayload(payload.data, "employees", payload.method),
      };

      if (payload.method === "PATCH") {
        updateObj.endpoint = `/employees/${payload.resourceId}`;
        updateObj.resourceId = payload.resourceId;
      }

      if (payload.method === "POST") {
        updateObj.endpoint = "/employees";
      }

      return updateObj;
    });

    return mappedEmployeeUpdateResources;
  }

  generateContractorUpdateData(payloadCollection: PayloadData[]) {
    const mappedContractorUpdateResources = payloadCollection.map((payload) => {
      const updateObj: Partial<UpdateResourceObj> = {
        resourceName: "contractors",
        method: payload.method,
        data: this.handleCreateCheckPayload(payload.data, "contractors", payload.method),
      };

      if (payload.method === "PATCH") {
        updateObj.endpoint = `/contractors/${payload.resourceId}`;
        updateObj.resourceId = payload.resourceId;
      }

      if (payload.method === "POST") {
        updateObj.endpoint = "/contractors";
      }

      return updateObj;
    });

    return mappedContractorUpdateResources;
  }

  generateCompanyUpdateData(payloadCollection: PayloadData[]) {
    const mappedCompanyUpdateResources = payloadCollection.map((payload) => {
      const updateObj: Partial<UpdateResourceObj> = {
        resourceName: "companies",
        method: payload.method,
        data: this.handleCreateCheckPayload(payload.data, "companies", payload.method),
      };

      if (payload.method === "PATCH") {
        updateObj.endpoint = `/companies/${payload.resourceId}`;
        updateObj.resourceId = payload.resourceId;
      }

      if (payload.method === "POST") {
        updateObj.endpoint = "/companies";
      }

      return updateObj;
    });

    return mappedCompanyUpdateResources;
  }

  async getAllEmployees(organization: Organization, bulkEmployeeQueryString = "") {
    // the "limit" query parameter Check is limited to 100 items returned at a time
    let endpoint = `/employees?company=${organization.checkCompanyId}&limit=100`;

    if (bulkEmployeeQueryString) {
      endpoint += `&${bulkEmployeeQueryString}`;
    }

    return await this.loadAllPaginatedData<CheckEmployee>(endpoint);
  }

  async getAllContractors(organization: Organization, bulkContractorQueryString = "") {
    // the "limit" query parameter Check is limited to 100 items returned at a time
    let endpoint = `/contractors?company=${organization.checkCompanyId}&limit=100`;

    if (bulkContractorQueryString) {
      endpoint += `&${bulkContractorQueryString}`;
    }

    return await this.loadAllPaginatedData<CheckContractor>(endpoint);
  }

  public async downloadFile(urlOrEndpoint: string): Promise<Response> {
    // right now all the requests to download files use the full url
    const isFullUrl = urlOrEndpoint.startsWith("http");
    const url = isFullUrl ? urlOrEndpoint : `${this.baseUrl}${urlOrEndpoint}`;

    return this.requestWithFullUrl("GET", url, {
      redirect: "follow",
    });
  }
}
